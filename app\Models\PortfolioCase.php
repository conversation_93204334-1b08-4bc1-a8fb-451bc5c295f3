<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

class PortfolioCase extends Model
{
    use HasFactory;

    protected $table = 'cases';

    protected $fillable = [
        'slug',
        'title',
        'description',
        'company_name',
        'logo',
        'images',
        'thumbnail',
        'portfolio_tag_id',
        'is_published',
    ];

    protected $casts = [
        'images' => 'array',
        'is_published' => 'boolean',
    ];

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($case) {
            if (empty($case->slug)) {
                $case->slug = Str::slug($case->title);
            }
        });

        static::updating(function ($case) {
            if ($case->isDirty('title') && empty($case->slug)) {
                $case->slug = Str::slug($case->title);
            }
        });
    }

    /**
     * Get the portfolio tag associated with this case.
     */
    public function portfolioTag()
    {
        return $this->belongsTo(PortfolioTag::class);
    }

    public function getRouteKeyName()
    {
        return 'slug';
    }
}
